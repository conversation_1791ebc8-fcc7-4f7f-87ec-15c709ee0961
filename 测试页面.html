<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>滚动插件测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      line-height: 1.6;
    }
    
    .content-block {
      background: #f4f4f4;
      margin: 20px 0;
      padding: 20px;
      border-radius: 8px;
      min-height: 300px;
    }
    
    .dynamic-content {
      background: #e8f5e8;
      border-left: 4px solid #4caf50;
    }
    
    #load-more {
      display: block;
      margin: 20px auto;
      padding: 10px 20px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    
    #load-more:hover {
      background: #0056b3;
    }
  </style>
</head>
<body>
  <h1>滚动插件测试页面</h1>
  <p>这是一个用于测试自动滚动插件的页面。页面包含多个内容块，并且可以动态加载更多内容。</p>
  
  <div class="content-block">
    <h2>内容块 1</h2>
    <p>这是第一个内容块。插件应该能够检测到页面的滚动并自动滚动到底部。</p>
  </div>
  
  <div class="content-block">
    <h2>内容块 2</h2>
    <p>这是第二个内容块。当点击滚动按钮时，页面应该平滑滚动到最底部。</p>
  </div>
  
  <div class="content-block">
    <h2>内容块 3</h2>
    <p>这是第三个内容块。插件会持续监控页面高度的变化。</p>
  </div>
  
  <div id="dynamic-container">
    <!-- 动态内容将在这里加载 -->
  </div>
  
  <button id="load-more">加载更多内容</button>
  
  <script>
    let contentCounter = 4;
    
    document.getElementById('load-more').addEventListener('click', function() {
      const container = document.getElementById('dynamic-container');
      
      // 创建新的内容块
      const newBlock = document.createElement('div');
      newBlock.className = 'content-block dynamic-content';
      newBlock.innerHTML = `
        <h2>动态内容块 ${contentCounter}</h2>
        <p>这是动态加载的内容块 ${contentCounter}。滚动插件应该能够检测到新内容并继续滚动到新的底部。</p>
        <p>内容加载时间: ${new Date().toLocaleTimeString()}</p>
      `;
      
      container.appendChild(newBlock);
      contentCounter++;
      
      // 模拟网络延迟
      setTimeout(() => {
        console.log('新内容已加载');
      }, 100);
    });
    
    // 自动加载一些初始内容
    setTimeout(() => {
      document.getElementById('load-more').click();
    }, 1000);
  </script>
</body>
</html>
