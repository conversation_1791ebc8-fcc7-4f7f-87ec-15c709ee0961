<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>滚动插件测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      line-height: 1.6;
    }

    .content-block {
      background: #f4f4f4;
      margin: 20px 0;
      padding: 20px;
      border-radius: 8px;
      min-height: 400px;
    }

    .dynamic-content {
      background: #e8f5e8;
      border-left: 4px solid #4caf50;
    }

    #load-more {
      display: block;
      margin: 20px auto;
      padding: 10px 20px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
    }

    #load-more:hover {
      background: #0056b3;
    }

    .test-info {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
  </style>

  <!-- 直接在页面中包含插件代码进行测试 -->
  <link rel="stylesheet" href="样式.css">
</head>
<body>
  <div class="test-info">
    <h2>🧪 滚动插件测试页面</h2>
    <p><strong>测试说明：</strong></p>
    <ul>
      <li>右下角应该有一个蓝色的圆形按钮（显示 ↓）</li>
      <li>点击按钮开始自动滚动到页面底部</li>
      <li>滚动时按钮变为橙色（显示 ⏸）</li>
      <li>再次点击可以停止滚动</li>
      <li>点击"加载更多内容"测试动态内容滚动</li>
    </ul>
  </div>

  <div class="content-block">
    <h2>📄 内容块 1</h2>
    <p>这是第一个内容块。插件应该能够检测到页面的滚动并自动滚动到底部。</p>
    <p>当前时间: <span id="time1"></span></p>
  </div>

  <div class="content-block">
    <h2>📄 内容块 2</h2>
    <p>这是第二个内容块。当点击滚动按钮时，页面应该平滑滚动到最底部。</p>
    <p>当前时间: <span id="time2"></span></p>
  </div>

  <div class="content-block">
    <h2>📄 内容块 3</h2>
    <p>这是第三个内容块。插件会持续监控页面高度的变化。</p>
    <p>当前时间: <span id="time3"></span></p>
  </div>

  <div class="content-block">
    <h2>📄 内容块 4</h2>
    <p>这是第四个内容块。页面足够长，可以测试滚动功能。</p>
    <p>当前时间: <span id="time4"></span></p>
  </div>

  <div class="content-block">
    <h2>📄 内容块 5</h2>
    <p>这是第五个内容块。继续增加页面高度。</p>
    <p>当前时间: <span id="time5"></span></p>
  </div>

  <div id="dynamic-container">
    <!-- 动态内容将在这里加载 -->
  </div>

  <button id="load-more">🔄 加载更多内容</button>

  <div class="content-block" style="background: #ffebee;">
    <h2>🎯 页面底部标记</h2>
    <p>如果滚动插件工作正常，点击按钮后应该能看到这个区域。</p>
    <p>最后更新时间: <span id="bottom-time"></span></p>
  </div>

  <!-- 包含插件脚本 -->
  <script src="内容脚本.js"></script>

  <script>
    // 更新时间显示
    function updateTimes() {
      const now = new Date().toLocaleTimeString();
      for (let i = 1; i <= 5; i++) {
        const element = document.getElementById(`time${i}`);
        if (element) element.textContent = now;
      }
      const bottomElement = document.getElementById('bottom-time');
      if (bottomElement) bottomElement.textContent = now;
    }

    // 每秒更新时间
    setInterval(updateTimes, 1000);
    updateTimes();

    let contentCounter = 6;

    document.getElementById('load-more').addEventListener('click', function() {
      const container = document.getElementById('dynamic-container');

      // 创建新的内容块
      const newBlock = document.createElement('div');
      newBlock.className = 'content-block dynamic-content';
      newBlock.innerHTML = `
        <h2>🆕 动态内容块 ${contentCounter}</h2>
        <p>这是动态加载的内容块 ${contentCounter}。滚动插件应该能够检测到新内容并继续滚动到新的底部。</p>
        <p>内容加载时间: ${new Date().toLocaleTimeString()}</p>
        <p>这个内容块有足够的高度来测试滚动功能。</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
      `;

      container.appendChild(newBlock);
      contentCounter++;

      console.log(`新内容块 ${contentCounter - 1} 已加载`);
    });
  </script>
</body>
</html>
