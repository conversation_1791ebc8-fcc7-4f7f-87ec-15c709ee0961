/* 悬浮滚动按钮样式 */
.auto-scroll-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background-color: rgba(0, 123, 255, 0.7);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  z-index: 9999;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  font-weight: bold;
}

.auto-scroll-button:hover {
  background-color: rgba(0, 123, 255, 0.9);
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.auto-scroll-button:active {
  transform: scale(0.95);
}

/* 滚动动画效果 */
.auto-scroll-button.scrolling {
  background-color: rgba(255, 165, 0, 0.8);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auto-scroll-button {
    width: 50px;
    height: 50px;
    bottom: 20px;
    right: 20px;
    font-size: 20px;
  }
}
