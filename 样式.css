/* 悬浮滚动按钮样式 */
.auto-scroll-button {
  position: fixed !important;
  bottom: 30px !important;
  right: 30px !important;
  width: 60px !important;
  height: 60px !important;
  background-color: rgba(0, 123, 255, 0.8) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  z-index: 999999 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), 0 0 0 2px rgba(0, 123, 255, 0.3) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 24px !important;
  color: white !important;
  font-weight: bold !important;
  font-family: Arial, sans-serif !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
}

.auto-scroll-button:hover {
  background-color: rgba(0, 123, 255, 0.95) !important;
  transform: scale(1.1) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5), 0 0 0 3px rgba(0, 123, 255, 0.5) !important;
}

.auto-scroll-button:active {
  transform: scale(0.95) !important;
}

/* 滚动动画效果 */
.auto-scroll-button.scrolling {
  background-color: rgba(255, 165, 0, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  animation: pulse 1.5s infinite, rotate 3s linear infinite !important;
}

/* 多种动画效果 */
@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auto-scroll-button {
    width: 50px;
    height: 50px;
    bottom: 20px;
    right: 20px;
    font-size: 20px;
  }
}
