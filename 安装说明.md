# 自动滚动插件安装说明

## 功能特点

- ✅ 无图标设计，使用简洁的箭头符号
- ✅ 半透明悬浮按钮，不遮挡页面内容
- ✅ 智能检测动态加载内容
- ✅ 平滑滚动动画效果
- ✅ 响应式设计，适配移动端
- ✅ 一键开始/停止滚动

## 安装步骤

### Chrome/Edge 浏览器

1. 打开浏览器，进入扩展程序管理页面：
   - Chrome: `chrome://extensions/`
   - Edge: `edge://extensions/`

2. 开启"开发者模式"（右上角开关）

3. 点击"加载已解压的扩展程序"

4. 选择包含插件文件的文件夹（包含 manifest.json 的文件夹）

5. 插件安装完成！

### Firefox 浏览器

1. 打开 `about:debugging`
2. 点击"此 Firefox"
3. 点击"临时载入附加组件"
4. 选择 manifest.json 文件

## 使用方法

1. 安装插件后，在任何网页上都会看到右下角的蓝色圆形按钮
2. 点击按钮开始自动滚动到页面底部
3. 滚动过程中按钮变为橙色，显示暂停图标
4. 再次点击按钮可以停止滚动
5. 插件会自动检测新加载的内容并继续滚动

## 测试插件

1. 在浏览器中打开 `测试页面.html`
2. 观察右下角的滚动按钮
3. 点击"加载更多内容"按钮添加动态内容
4. 测试滚动功能是否正常工作

## 技术特性

- **智能滚动**: 每秒检查页面高度变化
- **动态适应**: 自动处理 AJAX 加载的内容
- **性能优化**: 使用 MutationObserver 监听 DOM 变化
- **用户体验**: 平滑滚动动画和视觉反馈

## 故障排除

如果插件不工作，请检查：

1. 确保所有文件都在同一文件夹中
2. 检查浏览器控制台是否有错误信息
3. 确认插件已正确加载并启用
4. 刷新页面重新加载插件

## 文件结构

```
滚动插件/
├── manifest.json      # 插件配置文件
├── 内容脚本.js        # 主要功能脚本
├── 样式.css          # 按钮样式
├── 测试页面.html      # 测试页面
└── 安装说明.md        # 本说明文件
```
