// 自动滚动插件主脚本
(function() {
    'use strict';

    let scrollButton = null;
    let isScrolling = false;
    let scrollInterval = null;

    // 创建悬浮按钮
    function createScrollButton() {
        if (scrollButton) return;

        scrollButton = document.createElement('button');
        scrollButton.className = 'auto-scroll-button';
        scrollButton.innerHTML = '↓';
        scrollButton.title = '点击滚动到底部';

        // 添加点击事件
        scrollButton.addEventListener('click', handleButtonClick);

        // 添加到页面
        document.body.appendChild(scrollButton);

        console.log('滚动按钮已创建');
    }

    // 处理按钮点击
    function handleButtonClick() {
        console.log('按钮被点击，当前滚动状态:', isScrolling);

        if (isScrolling) {
            stopAutoScroll();
        } else {
            startAutoScroll();
        }
    }

    // 开始自动滚动
    function startAutoScroll() {
        console.log('开始自动滚动');

        isScrolling = true;
        scrollButton.classList.add('scrolling');
        scrollButton.innerHTML = '⏸';
        scrollButton.title = '点击停止滚动';

        // 立即滚动一次
        scrollToBottomOnce();

        // 然后开始定期检查和滚动
        scrollInterval = setInterval(() => {
            if (!isScrolling) return;
            scrollToBottomOnce();
        }, 1500); // 每1.5秒检查一次
    }

    // 停止自动滚动
    function stopAutoScroll() {
        console.log('停止自动滚动');

        isScrolling = false;
        scrollButton.classList.remove('scrolling');
        scrollButton.innerHTML = '↓';
        scrollButton.title = '点击滚动到底部';

        if (scrollInterval) {
            clearInterval(scrollInterval);
            scrollInterval = null;
        }
    }

    // 单次滚动到底部
    function scrollToBottomOnce() {
        const currentScrollHeight = document.documentElement.scrollHeight;
        const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

        console.log('当前页面高度:', currentScrollHeight, '当前滚动位置:', currentScrollTop);

        // 直接滚动到页面最底部
        window.scrollTo({
            top: currentScrollHeight,
            behavior: 'smooth'
        });

        // 备用方法：如果上面的方法不工作，使用这个
        setTimeout(() => {
            if (isScrolling) {
                document.documentElement.scrollTop = document.documentElement.scrollHeight;
                document.body.scrollTop = document.body.scrollHeight;
            }
        }, 500);
    }

    // 页面加载完成后初始化
    function init() {
        console.log('初始化滚动插件');

        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createScrollButton);
        } else {
            createScrollButton();
        }

        // 监听页面变化（处理单页应用）
        const observer = new MutationObserver((mutations) => {
            if (!scrollButton && document.body) {
                createScrollButton();
            }
        });

        if (document.body) {
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    // 清理函数
    function cleanup() {
        if (scrollButton) {
            scrollButton.remove();
            scrollButton = null;
        }
        if (scrollInterval) {
            clearInterval(scrollInterval);
            scrollInterval = null;
        }
        isScrolling = false;
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', cleanup);

    // 初始化插件
    init();

})();
