// 自动滚动插件主脚本
(function() {
    'use strict';
    
    let scrollButton = null;
    let isScrolling = false;
    let scrollInterval = null;
    
    // 创建悬浮按钮
    function createScrollButton() {
        if (scrollButton) return;
        
        scrollButton = document.createElement('button');
        scrollButton.className = 'auto-scroll-button';
        scrollButton.innerHTML = '↓';
        scrollButton.title = '点击滚动到底部';
        
        // 添加点击事件
        scrollButton.addEventListener('click', startAutoScroll);
        
        // 添加到页面
        document.body.appendChild(scrollButton);
    }
    
    // 开始自动滚动
    function startAutoScroll() {
        if (isScrolling) {
            stopAutoScroll();
            return;
        }
        
        isScrolling = true;
        scrollButton.classList.add('scrolling');
        scrollButton.innerHTML = '⏸';
        scrollButton.title = '点击停止滚动';
        
        // 持续滚动到底部
        scrollToBottom();
    }
    
    // 停止自动滚动
    function stopAutoScroll() {
        isScrolling = false;
        scrollButton.classList.remove('scrolling');
        scrollButton.innerHTML = '↓';
        scrollButton.title = '点击滚动到底部';
        
        if (scrollInterval) {
            clearInterval(scrollInterval);
            scrollInterval = null;
        }
    }
    
    // 滚动到底部的核心函数
    function scrollToBottom() {
        let lastScrollHeight = 0;
        let stableCount = 0;
        
        scrollInterval = setInterval(() => {
            if (!isScrolling) return;
            
            const currentScrollHeight = document.documentElement.scrollHeight;
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            
            // 滚动到当前页面底部
            window.scrollTo({
                top: currentScrollHeight,
                behavior: 'smooth'
            });
            
            // 检查是否有新内容加载
            if (currentScrollHeight === lastScrollHeight) {
                stableCount++;
                // 如果连续3次检查都没有新内容，说明已经到达真正的底部
                if (stableCount >= 3) {
                    // 继续保持滚动状态，等待可能的新内容
                    // 不自动停止，让用户手动控制
                }
            } else {
                stableCount = 0;
                lastScrollHeight = currentScrollHeight;
            }
            
            // 检查是否接近底部
            const isNearBottom = (currentScrollTop + windowHeight) >= (currentScrollHeight - 100);
            if (!isNearBottom) {
                // 如果不在底部，继续滚动
                window.scrollTo({
                    top: currentScrollHeight,
                    behavior: 'smooth'
                });
            }
            
        }, 1000); // 每秒检查一次
    }
    
    // 页面加载完成后初始化
    function init() {
        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createScrollButton);
        } else {
            createScrollButton();
        }
        
        // 监听页面变化（处理单页应用）
        const observer = new MutationObserver((mutations) => {
            if (!scrollButton && document.body) {
                createScrollButton();
            }
        });
        
        if (document.body) {
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    // 清理函数
    function cleanup() {
        if (scrollButton) {
            scrollButton.remove();
            scrollButton = null;
        }
        if (scrollInterval) {
            clearInterval(scrollInterval);
            scrollInterval = null;
        }
        isScrolling = false;
    }
    
    // 页面卸载时清理
    window.addEventListener('beforeunload', cleanup);
    
    // 初始化插件
    init();
    
})();
